module Api
  module V1
    class ChatController < ApplicationController
      before_action :require_user
      
      # GET /api/v1/chat/users
      # Returns all users for the chat with their online status and unread counts
      def users
        @users = fetch_users_except_current
        users_with_status = build_users_with_status(@users)

        # Get unread message counts for each user
        unread_counts = {}
        @users.each do |user|
          unread_count = Message.where(
            user_id: user.id,
            recipient_id: @current_user.id,
            message_type: 'chat',
            seen_at: nil
          ).count
          unread_counts[user.id] = unread_count if unread_count > 0
        end

        render json: {
          success: true,
          users: users_with_status,
          unread_counts: unread_counts
        }
      end

      # GET /api/v1/chat/messages
      # Returns messages between current user and another user
      def messages
        recipient_id = params[:recipient_id].to_i

        # Get all messages between current user and recipient
        @messages = Message.where(
          "(user_id = ? AND recipient_id = ?) OR (user_id = ? AND recipient_id = ?)",
          @current_user.id, recipient_id, recipient_id, @current_user.id
        ).where(message_type: 'chat')
        .order(created_at: :asc)

        # Mark messages from the other user as seen
        mark_messages_as_seen(recipient_id)

        render json: { messages: @messages }
      end
      
      # POST /api/v1/chat/messages
      # Creates a new chat message
      def create
        @message = Message.new(
          user_id: @current_user.id,
          recipient_id: params[:message][:recipient_id],
          body: params[:message][:body],
          message_type: 'chat',
          delivered_at: Time.now
        )

        if @message.save
          # Broadcasting is now handled by ChatChannel only
          # This ensures consistent real-time delivery through ActionCable
          render json: { success: true, message: @message }
        else
          render json: { success: false, errors: @message.errors.full_messages }, status: :unprocessable_entity
        end
      end
      
      # POST /api/v1/chat/mark_as_read
      # Marks messages as read
      def mark_as_read
        sender_id = params[:sender_id].to_i

        return render_invalid_sender_error if sender_id.zero?

        updated_count = mark_unread_messages_as_seen(sender_id)
        broadcast_read_status_update(sender_id) if updated_count.positive?

        render json: {
          success: true,
          messages_marked_as_read: updated_count,
          read_at: Time.current.iso8601
        }
      end

      # GET /api/v1/chat/unread_count
      # Returns total unread chat messages count for navigation badge
      def unread_count
        total_count = Message.where(recipient_id: @current_user.id, message_type: 'chat', seen_at: nil).count

        render json: {
          success: true,
          unread_count: total_count
        }
      end

      # POST /api/v1/chat/update_status
      # Updates the current user's online status and returns all user data
      def update_status
        online = parse_online_status
        update_and_broadcast_status(online)

        # Get all active users except current user
        users = ::User.active.where.not(id: @current_user.id).select(:id, :name, :avatar_image_url)

        # Get online statuses for all users
        online_statuses = UserOnlineStatus.where(user_id: users.pluck(:id))
                                          .index_by(&:user_id)

        # Build users array with online status
        users_with_status = users.map do |user|
          status = online_statuses[user.id]
          is_online = status && status.last_seen_at > 5.minutes.ago

          {
            id: user.id,
            name: user.name,
            avatar_url: user.avatar_image_url,
            online: is_online
          }
        end

        # Get unread counts
        unread_counts = Message.where(recipient_id: @current_user.id, message_type: 'chat', seen_at: nil)
                               .group(:user_id)
                               .count

        render json: {
          success: true,
          users: users_with_status,
          unread_counts: unread_counts
        }
      end

      private

      def fetch_users_except_current
        ::User.active.where.not(id: @current_user.id).where(deleted_at: nil).select(:id, :name, :avatar_image_url)
      end

      def build_users_with_status(users)
        user_ids = users.map(&:id)
        online_statuses = UserOnlineStatus.where(user_id: user_ids).index_by(&:user_id)

        users.map do |user|
          status = online_statuses[user.id]
          is_online = status ? status.online? : false
          {
            id: user.id,
            name: user.name,
            avatar_image_url: user.avatar_image_url,
            online: is_online,
            status: is_online ? 'online' : 'offline',
            last_seen_at: status&.last_seen_at
          }
        end
      end

      def parse_online_status
        if request.content_type&.include?('application/json')
          parse_json_online_status
        else
          parse_form_online_status
        end
      end

      def parse_json_online_status
        body = request.body.read
        begin
          parsed_params = JSON.parse(body)
          [true, 'true'].include?(parsed_params['online'])
        rescue JSON::ParserError
          false
        end
      end

      def parse_form_online_status
        [true, 'true'].include?(params[:online])
      end

      def update_and_broadcast_status(online)
        UserOnlineStatus.update_status(@current_user.id, online: online)
        ActionCable.server.broadcast('presence_channel', {
                                       user_id: @current_user.id,
                                       online: online
                                     })
      end

      def mark_messages_as_seen(sender_id)
        # Mark all unread messages from the sender as seen
        Message.where(
          user_id: sender_id,
          recipient_id: @current_user.id,
          message_type: 'chat',
          seen_at: nil
        ).update_all(seen_at: Time.current)
      end

      def render_invalid_sender_error
        render json: { success: false, error: 'Sender ID is required' }, status: :bad_request
      end

      def mark_unread_messages_as_seen(sender_id)
        Message.where(
          user_id: sender_id,
          recipient_id: @current_user.id,
          message_type: 'chat',
          seen_at: nil
        ).update_all(seen_at: Time.current)
      end

      def broadcast_read_status_update(sender_id)
        read_data = {
          type: 'messages_read',
          reader_id: @current_user.id,
          sender_id: sender_id,
          read_at: Time.current.iso8601
        }

        ActionCable.server.broadcast("chat_#{sender_id}_#{@current_user.id}", read_data)
        ActionCable.server.broadcast("chat_#{@current_user.id}_#{sender_id}", read_data)
      end
    end
  end
end
